import { useEffect } from 'react';

/**
 * Hook to prevent layout shift when scrollbar disappears due to modal/dropdown opening
 * This compensates for the scrollbar width by adding padding to the body
 */
export const useScrollbarCompensation = (isOpen: boolean) => {
  useEffect(() => {
    if (!isOpen) return;

    // Get the scrollbar width
    const getScrollbarWidth = () => {
      const outer = document.createElement('div');
      outer.style.visibility = 'hidden';
      outer.style.overflow = 'scroll';
      outer.style.msOverflowStyle = 'scrollbar';
      document.body.appendChild(outer);

      const inner = document.createElement('div');
      outer.appendChild(inner);

      const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
      outer.parentNode?.removeChild(outer);

      return scrollbarWidth;
    };

    const scrollbarWidth = getScrollbarWidth();
    const body = document.body;
    const hasVerticalScrollbar = body.scrollHeight > body.clientHeight;

    if (hasVerticalScrollbar && scrollbarWidth > 0) {
      // Store original values
      const originalPaddingRight = body.style.paddingRight;
      const originalOverflow = body.style.overflow;

      // Apply compensation
      body.style.paddingRight = `${scrollbarWidth}px`;
      body.style.overflow = 'hidden';

      // Cleanup function
      return () => {
        body.style.paddingRight = originalPaddingRight;
        body.style.overflow = originalOverflow;
      };
    }
  }, [isOpen]);
};

/**
 * Utility function to get scrollbar width
 */
export const getScrollbarWidth = (): number => {
  if (typeof window === 'undefined') return 0;

  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll';
  outer.style.msOverflowStyle = 'scrollbar';
  document.body.appendChild(outer);

  const inner = document.createElement('div');
  outer.appendChild(inner);

  const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
  outer.parentNode?.removeChild(outer);

  return scrollbarWidth;
};
