@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sohnus Job Platform Design System - All colors MUST be HSL */
/* Design Token Architecture:
 * - Base tokens: Shared across all themes
 * - Job Seeker theme: Warm, approachable, energetic (yellow/orange)
 * - Employer theme: Professional, trustworthy, authoritative (blue/teal)
 */

@layer base {
  :root {
    /* Base Design Tokens - Shared across all themes */
    --radius: 12px;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Default theme (Job Seeker) */
    --background: 0 0% 100%;
    --foreground: 244.25, 100%, 22.16%;

    --card: 0 0% 100%/0.7;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    /* Brand Colors - Job Seeker Theme (Warm, Energetic) */
    --primary: 56 100% 58%;
    --primary-foreground: 248 100% 22%;
    --primary-glow: 56 100% 70%;
    --primary-hover: 56 100% 52%;
    --primary-active: 56 100% 48%;

    --secondary: 244.25, 100%, 22.16%;
    --secondary-foreground: 0 0% 100%;
    --secondary-dark: 256 100% 12.2%;
    --secondary-hover: 244.25, 100%, 28%;
    --secondary-active: 244.25, 100%, 32%;

    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;
    --accent-hover: 217 91% 55%;
    --accent-active: 217 91% 50%;

    --muted: 220 13% 97%;
    --muted-foreground: 220 9% 46%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 56 100% 58%;

    /* Job Seeker Theme Specific Tokens */
    --gradient-hero: linear-gradient(135deg, hsl(56 100% 58%), hsl(56 100% 70%));
    --gradient-accent: linear-gradient(135deg, hsl(217 91% 60%), hsl(217 91% 70%));
    --gradient-card: linear-gradient(135deg, hsl(56 100% 95%), hsl(56 100% 98%));
    --shadow-soft: 0 4px 20px -2px hsl(56 100% 58% / 0.15);
    --shadow-card: 0 8px 25px -5px hsl(220 13% 18% / 0.1);
    --shadow-button: 0 2px 8px -1px hsl(56 100% 58% / 0.2);

    /* Form specific tokens */
    --form-background: 0 0% 100%;
    --form-border: 220 13% 91%;
    --form-focus: 56 100% 58%;
    --form-error: 0 84% 60%;
    --form-success: 142 76% 36%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 56 100% 50%;
    --primary-foreground: 220 13% 18%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 56 100% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Job Seeker Theme - Explicit class for consistency */
  .job-seeker-theme {
    /* Inherits from :root - warm, energetic theme */
    /* This class exists for explicit theming when needed */
  }

  /* Employer Theme - Professional, trustworthy color scheme */
  .employer-theme {
    /* Primary colors - Professional blue/teal theme */
    --primary: 195 100% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 195 100% 55%;
    --primary-hover: 195 100% 40%;
    --primary-active: 195 100% 35%;

    /* Secondary colors - Deep navy */
    --secondary: 220 100% 25%;
    --secondary-foreground: 0 0% 100%;
    --secondary-dark: 220 100% 15%;
    --secondary-hover: 220 100% 30%;
    --secondary-active: 220 100% 35%;

    /* Accent colors - Complementary orange */
    --accent: 25 95% 55%;
    --accent-foreground: 0 0% 100%;
    --accent-hover: 25 95% 50%;
    --accent-active: 25 95% 45%;

    /* Background adjustments for employer theme */
    --background: 210 20% 98%;
    --foreground: 220 100% 15%;

    /* Card styling for employer theme */
    --card: 0 0% 100%/0.85;
    --card-foreground: 220 100% 20%;

    /* Employer Theme Specific Tokens */
    --gradient-hero: linear-gradient(135deg, hsl(195 100% 45%), hsl(195 100% 55%));
    --gradient-accent: linear-gradient(135deg, hsl(25 95% 55%), hsl(25 95% 65%));
    --gradient-card: linear-gradient(135deg, hsl(195 100% 96%), hsl(195 100% 98%));
    --shadow-soft: 0 4px 20px -2px hsl(195 100% 45% / 0.15);
    --shadow-card: 0 8px 25px -5px hsl(220 100% 15% / 0.12);
    --shadow-button: 0 2px 8px -1px hsl(195 100% 45% / 0.25);

    /* Form specific tokens for employer theme */
    --form-background: 0 0% 100%;
    --form-border: 220 13% 88%;
    --form-focus: 195 100% 45%;
    --form-error: 0 84% 60%;
    --form-success: 142 76% 36%;

    /* Ring color for focus states */
    --ring: 195 100% 45%;

    /* Muted colors adjusted for employer theme */
    --muted: 210 20% 95%;
    --muted-foreground: 220 15% 40%;

    /* Border and input adjustments */
    --border: 220 13% 88%;
    --input: 220 13% 88%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .shadow-3xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Fix for floating action button position stability when dropdowns open */
  [data-radix-dropdown-content] {
    /* Prevent dropdown from affecting body layout */
    position: fixed;
    z-index: 50;
  }

  /* Ensure fixed elements maintain their position during dropdown interactions */
  body:has([data-radix-dropdown-content]) .fixed {
    transform: translateZ(0);
  }

  /* Theme-specific utility classes */
  .job-seeker-hero-adjustments {
    /* Warm, energetic styling for job seekers */
    background: var(--gradient-hero);
  }

  .employer-hero-adjustments {
    /* Professional, trustworthy styling for employers */
    background: var(--gradient-hero);
  }

  .job-seeker-form-styling {
    /* Approachable form styling */
    --form-accent: var(--primary);
  }

  .employer-form-styling {
    /* Professional form styling */
    --form-accent: var(--primary);
  }

  .job-seeker-cta-emphasis {
    /* Energetic CTA styling */
    transform: scale(1);
    transition: var(--transition-smooth);
  }

  .job-seeker-cta-emphasis:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-button);
  }

  .employer-cta-emphasis {
    /* Professional CTA styling */
    transform: scale(1);
    transition: var(--transition-smooth);
  }

  .employer-cta-emphasis:hover {
    transform: scale(1.01);
    box-shadow: var(--shadow-button);
  }

  /* Design token utility classes */
  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-fast {
    transition: var(--transition-fast);
  }

  .transition-slow {
    transition: var(--transition-slow);
  }

  .shadow-button {
    box-shadow: var(--shadow-button);
  }

  .bg-gradient-card {
    background: var(--gradient-card);
  }

  /* Spacing utilities using design tokens */
  .p-token-xs { padding: var(--spacing-xs); }
  .p-token-sm { padding: var(--spacing-sm); }
  .p-token-md { padding: var(--spacing-md); }
  .p-token-lg { padding: var(--spacing-lg); }
  .p-token-xl { padding: var(--spacing-xl); }
  .p-token-2xl { padding: var(--spacing-2xl); }
  .p-token-3xl { padding: var(--spacing-3xl); }

  .m-token-xs { margin: var(--spacing-xs); }
  .m-token-sm { margin: var(--spacing-sm); }
  .m-token-md { margin: var(--spacing-md); }
  .m-token-lg { margin: var(--spacing-lg); }
  .m-token-xl { margin: var(--spacing-xl); }
  .m-token-2xl { margin: var(--spacing-2xl); }
  .m-token-3xl { margin: var(--spacing-3xl); }

  /* Gap utilities */
  .gap-token-xs { gap: var(--spacing-xs); }
  .gap-token-sm { gap: var(--spacing-sm); }
  .gap-token-md { gap: var(--spacing-md); }
  .gap-token-lg { gap: var(--spacing-lg); }
  .gap-token-xl { gap: var(--spacing-xl); }
  .gap-token-2xl { gap: var(--spacing-2xl); }
  .gap-token-3xl { gap: var(--spacing-3xl); }
}